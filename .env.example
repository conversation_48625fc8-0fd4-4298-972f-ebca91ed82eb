# SupplyLine MRO Suite Environment Configuration
# Copy this file to .env and update with your actual values

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-change-this-in-production
DEBUG=True

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/supplyline_mro
# For local development with SQLite (not recommended for production):
# DATABASE_URL=sqlite:///supplyline.db

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-here-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRES=900  # 15 minutes in seconds
JWT_REFRESH_TOKEN_EXPIRES=604800  # 7 days in seconds

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Security Configuration
BCRYPT_LOG_ROUNDS=12
SESSION_COOKIE_SECURE=False  # Set to True in production with HTTPS
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# AWS Configuration (for production)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Database Configuration for Production
RDS_HOSTNAME=your-rds-endpoint
RDS_PORT=5432
RDS_DB_NAME=supplyline_mro
RDS_USERNAME=your-db-username
RDS_PASSWORD=your-db-password

# Frontend Environment Variables
VITE_API_URL=http://localhost:5000

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=app.log

# Application Configuration
APP_NAME=SupplyLine MRO Suite
APP_VERSION=4.0.0
ADMIN_EMAIL=<EMAIL>

# Docker Compose Resource Limits (optional)
BACKEND_CPU_LIMIT=0.5
BACKEND_MEMORY_LIMIT=512M
FRONTEND_CPU_LIMIT=0.3
FRONTEND_MEMORY_LIMIT=256M
